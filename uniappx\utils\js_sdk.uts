// uniapp-x 简化版本的 JS SDK
// 在 uniapp-x 中，很多功能可以直接使用 uni API

export default {
    async init(title: string, info: string, url: string, img: string, options = {}) {
        console.log('初始化 uniapp-x SDK');
        
        // 在 uniapp-x 中，分享功能通过 uni.share 等 API 实现
        // 这里只是一个占位符，实际功能需要根据平台特性实现
        
        return Promise.resolve(true);
    },

    // 语音录音相关方法 - 使用 uni API
    voice: {
        // 开始录音
        startRecord() {
            return new Promise((resolve, reject) => {
                uni.startRecord({
                    success: function() {
                        console.log('开始录音成功');
                        resolve(true);
                    },
                    fail: function(err) {
                        console.error('开始录音失败:', err);
                        reject(err);
                    }
                });
            });
        },

        // 停止录音
        stopRecord() {
            return new Promise((resolve, reject) => {
                uni.stopRecord({
                    success: function(res) {
                        console.log('停止录音成功:', res);
                        resolve(res.tempFilePath);
                    },
                    fail: function(err) {
                        console.error('停止录音失败:', err);
                        reject(err);
                    }
                });
            });
        },

        // 播放语音
        playVoice(filePath: string) {
            return new Promise((resolve, reject) => {
                if (!filePath) {
                    reject(new Error('filePath不能为空'));
                    return;
                }
                
                uni.playVoice({
                    filePath: filePath,
                    success: function() {
                        console.log('播放语音成功');
                        resolve(true);
                    },
                    fail: function(err) {
                        console.error('播放语音失败:', err);
                        reject(err);
                    }
                });
            });
        },

        // 停止播放语音
        stopVoice() {
            return new Promise((resolve, reject) => {
                uni.stopVoice({
                    success: function() {
                        console.log('停止播放语音成功');
                        resolve(true);
                    },
                    fail: function(err) {
                        console.error('停止播放语音失败:', err);
                        reject(err);
                    }
                });
            });
        }
    },

    // 地理位置相关方法 - 使用 uni API
    location: {
        // 获取地理位置接口
        getLocation(type = 'gcj02') {
            return new Promise((resolve, reject) => {
                console.log('开始获取地理位置, type:', type);
                
                uni.getLocation({
                    type: type,
                    success: function (res) {
                        console.log('获取地理位置成功:', res);
                        
                        const locationData = {
                            latitude: parseFloat(res.latitude as string),
                            longitude: parseFloat(res.longitude as string),
                            speed: res.speed || 0,
                            accuracy: res.accuracy || 0
                        };
                        
                        console.log('处理后的位置数据:', locationData);
                        resolve(locationData);
                    },
                    fail: function(err) {
                        console.error('获取地理位置失败详情:', err);
                        
                        let errorMessage = '获取位置失败';
                        
                        if (err.errMsg) {
                            if (err.errMsg.includes('permission denied') || err.errMsg.includes('deny')) {
                                errorMessage = '用户拒绝了位置权限，请允许位置访问';
                            } else if (err.errMsg.includes('location service disabled')) {
                                errorMessage = '位置服务未开启，请在设置中开启定位服务';
                            } else if (err.errMsg.includes('timeout')) {
                                errorMessage = '获取位置超时，请检查网络连接';
                            } else {
                                errorMessage = `获取位置失败: ${err.errMsg}`;
                            }
                        }
                        
                        reject(new Error(errorMessage));
                    }
                });
            });
        },

        // 使用内置地图查看位置接口
        openLocation(options: any) {
            return new Promise((resolve, reject) => {
                if (!options || !options.latitude || !options.longitude) {
                    reject(new Error('latitude和longitude参数不能为空'));
                    return;
                }
                
                uni.openLocation({
                    latitude: options.latitude,
                    longitude: options.longitude,
                    name: options.name || '',
                    address: options.address || '',
                    scale: options.scale || 14,
                    success: function() {
                        console.log('打开地图成功');
                        resolve(true);
                    },
                    fail: function(err) {
                        console.error('打开地图失败:', err);
                        reject(err);
                    }
                });
            });
        }
    }
}
