/**
 * 日期格式化函数
 * @param date 日期对象
 * @param format 日期格式，默认为 YYYY-MM-DD HH:mm:ss
 */
export const formatDate = (date: Date, format = 'YYYY-MM-DD HH:mm:ss') => {
  // 获取年月日时分秒，通过 padStart 补 0
  const year = String(date.getFullYear())
  const month = String(date.getMonth() + 1).padStart(2, '0')
  const day = String(date.getDate()).padStart(2, '0')
  const hours = String(date.getHours()).padStart(2, '0')
  const minutes = String(date.getMinutes()).padStart(2, '0')
  const seconds = String(date.getSeconds()).padStart(2, '0')

  // 返回格式化后的结果
  return format
    .replace('YYYY', year)
    .replace('MM', month)
    .replace('DD', day)
    .replace('HH', hours)
    .replace('mm', minutes)
    .replace('ss', seconds)
}

/**
 * 计算从注册到现在天数
 * @param createTime 注册时间字符串
 * @returns 天数
 */
export const calculateDaysSinceRegistration = (createTime: string): number => {
  if (!createTime) return 0
  
  const createDate = new Date(createTime)
  const now = new Date()
  
  // 计算时间差（毫秒）
  const timeDiff = now.getTime() - createDate.getTime()
  
  // 转换为天数
  const daysDiff = Math.floor(timeDiff / (1000 * 60 * 60 * 24))
  
  return Math.max(0, daysDiff) // 确保返回非负数
}
